import React, { useState } from 'react';
import { useChatStore } from '../../store/useChatStore';
import { Chat } from '../../types/chat';
import { Search, Users, User, MoreVertical } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

const ChatList: React.FC = () => {
  const { 
    chats, 
    currentChat, 
    setCurrentChat, 
    loading, 
    chatHasMore, 
    loadChats, 
    chatPage 
  } = useChatStore();
  const [searchQuery, setSearchQuery] = useState('');

  // Filter chats based on search query
  const filteredChats = chats.filter(chat => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    
    // Search by chat name (for group chats)
    if (chat.name && chat.name.toLowerCase().includes(query)) {
      return true;
    }
    
    // Search by participant names
    return chat.participants.some(participant => 
      participant.username.toLowerCase().includes(query)
    );
  });

  const handleChatSelect = (chat: Chat) => {
    setCurrentChat(chat);
  };

  const loadMoreChats = () => {
    if (!loading && chatHasMore) {
      loadChats(chatPage + 1);
    }
  };

  const getChatDisplayName = (chat: Chat): string => {
    if (chat.type === 'group' && chat.name) {
      return chat.name;
    }
    
    // For direct chats, show the other participant's name
    if (chat.participants.length === 2) {
      const otherParticipant = chat.participants.find(p => p.id !== currentChat?.participants[0]?.id);
      return otherParticipant?.username || 'Unknown User';
    }
    
    return chat.participants.map(p => p.username).join(', ');
  };

  const getChatAvatar = (chat: Chat): string => {
    if (chat.type === 'group') {
      return chat.name?.charAt(0).toUpperCase() || 'G';
    }
    
    // For direct chats, use the other participant's avatar or initial
    if (chat.participants.length === 2) {
      const otherParticipant = chat.participants.find(p => p.id !== currentChat?.participants[0]?.id);
      return otherParticipant?.username?.charAt(0).toUpperCase() || 'U';
    }
    
    return 'C';
  };

  const formatLastMessageTime = (dateString: string): string => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return '';
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-neonBlue"
          />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {loading && chats.length === 0 ? (
          <div className="p-4 text-center text-gray-400">
            Loading conversations...
          </div>
        ) : filteredChats.length === 0 ? (
          <div className="p-4 text-center text-gray-400">
            {searchQuery ? 'No conversations found' : 'No conversations yet'}
          </div>
        ) : (
          <>
            {filteredChats.map((chat) => (
              <div
                key={chat.id}
                onClick={() => handleChatSelect(chat)}
                className={`p-4 border-b border-gray-700 cursor-pointer hover:bg-gray-700 transition-colors ${
                  currentChat?.id === chat.id ? 'bg-gray-700 border-l-4 border-l-neonBlue' : ''
                }`}
              >
                <div className="flex items-center">
                  {/* Avatar */}
                  <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold mr-3 relative">
                    {chat.type === 'group' ? (
                      <Users className="w-6 h-6" />
                    ) : (
                      <User className="w-6 h-6" />
                    )}
                    {chat.unreadCount > 0 && (
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                      </div>
                    )}
                  </div>

                  {/* Chat Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-white truncate">
                        {getChatDisplayName(chat)}
                      </h3>
                      {chat.lastMessage && (
                        <span className="text-xs text-gray-400 ml-2">
                          {formatLastMessageTime(chat.lastMessage.createdAt)}
                        </span>
                      )}
                    </div>
                    
                    {chat.lastMessage && (
                      <p className="text-sm text-gray-400 truncate mt-1">
                        {chat.lastMessage.sender.username}: {chat.lastMessage.content}
                      </p>
                    )}
                    
                    {chat.type === 'group' && (
                      <p className="text-xs text-gray-500 mt-1">
                        {chat.participants.length} participants
                      </p>
                    )}
                  </div>

                  {/* Options */}
                  <button className="p-1 text-gray-400 hover:text-white">
                    <MoreVertical className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}

            {/* Load More Button */}
            {chatHasMore && (
              <div className="p-4">
                <button
                  onClick={loadMoreChats}
                  disabled={loading}
                  className="w-full py-2 text-neonBlue hover:text-blue-400 disabled:text-gray-500 text-sm"
                >
                  {loading ? 'Loading...' : 'Load More'}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ChatList;
