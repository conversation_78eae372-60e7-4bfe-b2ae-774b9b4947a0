export interface User {
  id: number;
  username: string;
  email?: string;
  avatarUrl?: string;
  level?: number;
  experience?: number;
  energy?: number;
  strength?: number;
  endurance?: number;
  intelligence?: number;
  gold?: number;
  money?: number;
  isPremium?: boolean;
  premiumExpiresAt?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Chat {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  description?: string;
  participants: User[];
  lastMessage?: LastMessage;
  unreadCount: number;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
}

export interface LastMessage {
  id: string;
  content: string;
  type: string;
  status: string;
  sender: User;
  isRead: boolean;
  editedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  content: string;
  type: string;
  status: string;
  sender: User;
  chatId?: string;
  isRead: boolean;
  editedAt?: string;
  createdAt: string;
  updatedAt: string;
  readStatuses?: ReadStatus[];
}

export interface ReadStatus {
  id: string;
  user: User;
  readAt: string;
  createdAt: string;
}

export interface MessageReadStatus {
  id: string;
  userId: number;
  user: User;
  messageId: string;
  readAt: string;
}

export interface CreateChatDto {
  type: 'direct' | 'group';
  participantIds: number[];
  name?: string;
  description?: string;
}

export interface SendMessageDto {
  content: string;
}

export interface ChatListResponse {
  chats: Chat[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface MessageListResponse {
  messages: Message[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
}

export interface UnreadCountResponse {
  unreadCount: number;
}

export interface TypingEvent {
  chatId: string;
  userId: number;
  username: string;
  isTyping: boolean;
}

export interface MessageEvent {
  type: 'message_sent' | 'message_read' | 'user_joined' | 'user_left';
  chatId: string;
  message?: Message;
  userId?: number;
  username?: string;
}

// WebSocket event types
export interface WebSocketEvents {
  // Client to server
  join_chat: { chatId: string };
  leave_chat: { chatId: string };
  typing_start: { chatId: string };
  typing_stop: { chatId: string };

  // Server to client
  message_received: Message;
  message_read: { messageId: string; userId: number; readAt: string };
  user_typing: TypingEvent;
  user_stopped_typing: TypingEvent;
  chat_updated: Chat;
  error: { message: string; code?: string };
}
