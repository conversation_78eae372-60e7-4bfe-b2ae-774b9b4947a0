import { api } from './api';
import {
  Chat,
  Message,
  CreateChatDto,
  SendMessageDto,
  ChatListResponse,
  MessageListResponse,
  UnreadCountResponse
} from '../../types/chat';

export const chatService = {
  // Create a new chat
  createChat: async (createChatDto: CreateChatDto): Promise<Chat> => {
    const response = await api.post('/chats', createChatDto);
    return response.data;
  },

  // Get user's chats with pagination
  getChats: async (page: number = 1, limit: number = 20): Promise<ChatListResponse> => {
    const response = await api.get('/chats', {
      params: { page, limit }
    });
    return response.data;
  },

  // Get a specific chat by ID
  getChat: async (chatId: string): Promise<Chat> => {
    const response = await api.get(`/chats/${chatId}`);
    return response.data;
  },

  // Get messages for a chat with cursor-based pagination
  getMessages: async (
    chatId: string,
    cursor?: string,
    limit: number = 20
  ): Promise<MessageListResponse> => {
    const params: any = { limit };
    if (cursor) {
      params.cursor = cursor;
    }
    
    const response = await api.get(`/chats/${chatId}/messages`, { params });
    return response.data;
  },

  // Send a message to a chat
  sendMessage: async (chatId: string, messageDto: SendMessageDto): Promise<Message> => {
    const response = await api.post(`/chats/${chatId}/messages`, messageDto);
    return response.data;
  },

  // Mark messages as read
  markAsRead: async (chatId: string): Promise<void> => {
    await api.put(`/chats/${chatId}/read`);
  },

  // Get unread message count for a chat
  getUnreadCount: async (chatId: string): Promise<UnreadCountResponse> => {
    const response = await api.get(`/chats/${chatId}/unread-count`);
    return response.data;
  },

  // Get total unread count across all chats
  getTotalUnreadCount: async (): Promise<number> => {
    try {
      const chatsResponse = await api.get('/chats', { params: { page: 1, limit: 100 } });
      const chats: Chat[] = chatsResponse.data.chats;
      return chats.reduce((total, chat) => total + chat.unreadCount, 0);
    } catch (error) {
      console.error('Failed to get total unread count:', error);
      return 0;
    }
  },

  // Search for users to start a chat with
  searchUsers: async (query: string): Promise<any[]> => {
    const response = await api.get('/users/search', {
      params: { q: query, limit: 10 }
    });
    return response.data;
  }
};

export default chatService;
